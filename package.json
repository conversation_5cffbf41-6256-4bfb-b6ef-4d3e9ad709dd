{"name": "autobuilder-ai", "displayName": "AutoBuilder AI", "description": "Autonomous full-stack application builder with AI agents", "version": "1.0.0", "publisher": "autobuilder", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Programming Languages"], "keywords": ["ai", "automation", "code generation", "full-stack", "assistant"], "activationEvents": ["onStartupFinished", "onView:autobuilder.sidebar"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "autobuilder.start", "title": "Start AutoBuilder AI", "category": "AutoBuilder"}, {"command": "autobuilder.stop", "title": "Stop AutoBuilder AI", "category": "AutoBuilder"}, {"command": "autobuilder.regenerate", "title": "Regenerate Current Task", "category": "AutoBuilder"}, {"command": "autobuilder.fix", "title": "Fix Current Issues", "category": "AutoBuilder"}, {"command": "autobuilder.deploy", "title": "Deploy Application", "category": "AutoBuilder"}, {"command": "autobuilder.showSidebar", "title": "Show AutoBuilder Sidebar", "category": "AutoBuilder"}, {"command": "autobuilder.diagnostics", "title": "Run Diagnostics", "category": "AutoBuilder"}], "views": {"autobuilder": [{"id": "autobuilder.sidebar", "name": "AutoBuilder AI", "type": "webview", "when": "true"}]}, "viewsContainers": {"activitybar": [{"id": "autobuilder", "title": "AutoBuilder AI", "icon": "$(tools)"}]}, "configuration": {"title": "AutoBuilder AI", "properties": {"autobuilder.groqApiKeys": {"type": "array", "items": {"type": "string"}, "default": [], "description": "List of GroqCloud API keys for rotation"}, "autobuilder.defaultModel": {"type": "string", "default": "llama-3.3-70b-versatile", "description": "Default AI model to use"}, "autobuilder.maxRetries": {"type": "number", "default": 3, "description": "Maximum number of retries for failed operations"}, "autobuilder.enablePlaywright": {"type": "boolean", "default": true, "description": "Enable Playwright testing integration"}, "autobuilder.memoryPath": {"type": "string", "default": ".memory", "description": "Path to memory folder relative to workspace"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "16.x", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "@vscode/test-electron": "^2.2.0", "@vscode/vsce": "^2.15.0", "eslint": "^8.28.0", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.0", "playwright": "^1.40.0"}}