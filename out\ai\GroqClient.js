"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GroqClient = void 0;
const axios_1 = __importDefault(require("axios"));
const types_1 = require("../types");
class GroqClient {
    constructor() {
        this.apiKeys = [];
        this.currentKeyIndex = 0;
        this.currentModel = 'llama-3.3-70b-versatile';
        this.rateLimitDelay = 1000; // 1 second between requests
        this.lastRequestTime = 0;
        this.client = axios_1.default.create({
            baseURL: 'https://api.groq.com/openai/v1',
            timeout: 60000,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
    async initialize(apiKeys) {
        if (!apiKeys || apiKeys.length === 0) {
            throw new Error('At least one API key is required');
        }
        this.apiKeys = apiKeys.filter(key => key && key.trim().length > 0);
        this.currentKeyIndex = 0;
        // Test the first API key
        await this.testApiKey();
    }
    async testApiKey() {
        try {
            await this.makeRequest('Test connection', 'test', { maxTokens: 10, temperature: 0.1 });
        }
        catch (error) {
            console.warn('API key test failed, but continuing:', error);
        }
    }
    async generatePlan(prompt, context) {
        const systemPrompt = this.buildPlanningSystemPrompt(context);
        const userPrompt = this.buildPlanningUserPrompt(prompt, context);
        const response = await this.makeRequest(systemPrompt, userPrompt, {
            maxTokens: 4000,
            temperature: 0.7
        });
        return this.parsePlanResponse(response, prompt);
    }
    async generateCode(task, context) {
        const systemPrompt = this.buildCodeGenerationSystemPrompt(context);
        const userPrompt = this.buildCodeGenerationUserPrompt(task, context);
        const response = await this.makeRequest(systemPrompt, userPrompt, {
            maxTokens: 3000,
            temperature: 0.3
        });
        return this.parseCodeResponse(response);
    }
    async analyzeError(error, context) {
        const systemPrompt = this.buildErrorAnalysisSystemPrompt();
        const userPrompt = this.buildErrorAnalysisUserPrompt(error, context);
        const response = await this.makeRequest(systemPrompt, userPrompt, {
            maxTokens: 2000,
            temperature: 0.2
        });
        return this.parseErrorAnalysisResponse(response);
    }
    async generateTests(files, context) {
        const systemPrompt = this.buildTestGenerationSystemPrompt(context);
        const userPrompt = this.buildTestGenerationUserPrompt(files, context);
        const response = await this.makeRequest(systemPrompt, userPrompt, {
            maxTokens: 2500,
            temperature: 0.4
        });
        return this.parseTestResponse(response);
    }
    rotateApiKey() {
        if (this.apiKeys.length > 1) {
            this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
        }
    }
    getCurrentModel() {
        return this.currentModel;
    }
    async makeRequest(systemPrompt, userPrompt, options) {
        // Rate limiting
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        if (timeSinceLastRequest < this.rateLimitDelay) {
            await new Promise(resolve => setTimeout(resolve, this.rateLimitDelay - timeSinceLastRequest));
        }
        const currentKey = this.apiKeys[this.currentKeyIndex];
        try {
            const response = await this.client.post('/chat/completions', {
                model: this.currentModel,
                messages: [
                    { role: 'system', content: systemPrompt },
                    { role: 'user', content: userPrompt }
                ],
                max_tokens: options.maxTokens,
                temperature: options.temperature,
                stream: false
            }, {
                headers: {
                    'Authorization': `Bearer ${currentKey}`
                }
            });
            this.lastRequestTime = Date.now();
            if (!response.data.choices || response.data.choices.length === 0) {
                throw new Error('No response from AI model');
            }
            return response.data.choices[0].message.content;
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                if (error.response?.status === 401) {
                    // Invalid API key, try rotating
                    this.rotateApiKey();
                    if (this.currentKeyIndex === 0) {
                        // We've tried all keys
                        throw new Error('All API keys are invalid');
                    }
                    // Retry with next key
                    return this.makeRequest(systemPrompt, userPrompt, options);
                }
                else if (error.response?.status === 429) {
                    // Rate limited, wait and retry
                    await new Promise(resolve => setTimeout(resolve, 5000));
                    return this.makeRequest(systemPrompt, userPrompt, options);
                }
            }
            throw error;
        }
    }
    buildPlanningSystemPrompt(context) {
        return `You are an expert software architect. Your task is to create comprehensive project plans for full-stack applications.

CONTEXT:
- Workspace has ${context.workspaceFiles.totalFiles} files
- Primary languages: ${this.extractPrimaryLanguages(context.workspaceFiles)}
- Existing plan: ${context.existingPlan ? 'Yes' : 'No'}

REQUIREMENTS:
1. Respond with valid JSON only - NO markdown code blocks, NO triple-backtick wrapper
2. Include detailed technology stack
3. Break down into specific, actionable tasks
4. Estimate realistic timeframes
5. Consider existing codebase structure

JSON SCHEMA:
{
  "overview": "string",
  "technologies": [{"name": "string", "version": "string", "purpose": "string", "category": "frontend|backend|database|testing|deployment|tooling"}],
  "tasks": [{"name": "string", "description": "string", "agent": "architect|engineer|tester|fixer", "estimatedDuration": number, "priority": "low|medium|high|critical", "files": ["string"], "commands": ["string"]}]
}`;
    }
    buildPlanningUserPrompt(prompt, context) {
        const workspaceInfo = context.workspaceFiles.summary || 'Empty workspace';
        const existingPlanInfo = context.existingPlan ?
            `Existing plan: ${context.existingPlan.overview}` :
            'No existing plan';
        return `Create a detailed project plan for: ${prompt}

WORKSPACE INFO:
${workspaceInfo}

${existingPlanInfo}

USER PREFERENCES:
${context.userPreferences ? JSON.stringify(context.userPreferences, null, 2) : 'None specified'}

Generate a comprehensive plan with specific tasks, technologies, and implementation steps.`;
    }
    buildCodeGenerationSystemPrompt(context) {
        return `You are an expert software engineer. Generate high-quality, production-ready code.

CONTEXT:
- Target framework: ${context.targetFramework || 'Not specified'}
- Existing files: ${context.existingFiles.length}
- Dependencies: ${context.dependencies.join(', ')}

REQUIREMENTS:
1. Respond with valid JSON only - NO markdown code blocks, NO triple-backtick wrapper
2. Generate complete, working code files
3. Include proper error handling
4. Follow best practices and conventions
5. Include necessary commands and dependencies

JSON SCHEMA:
{
  "files": [{"path": "string", "content": "string", "language": "string", "description": "string"}],
  "commands": ["string"],
  "dependencies": ["string"],
  "notes": ["string"]
}`;
    }
    buildCodeGenerationUserPrompt(task, context) {
        return `Generate code for task: ${task.name}

DESCRIPTION: ${task.description}

PLAN OVERVIEW: ${context.plan.overview}

TECHNOLOGIES: ${context.plan.technologies.map(t => `${t.name} (${t.purpose})`).join(', ')}

EXISTING FILES: ${context.existingFiles.join(', ')}

TARGET FILES: ${task.files.join(', ')}

Generate complete, working code that implements this task according to the plan.`;
    }
    buildErrorAnalysisSystemPrompt() {
        return `You are an expert debugging specialist. Analyze errors and provide specific fixes.

REQUIREMENTS:
1. Respond with valid JSON only - NO markdown code blocks, NO triple-backtick wrapper
2. Identify root cause of errors
3. Provide specific, actionable fixes
4. Include prevention strategies

JSON SCHEMA:
{
  "rootCause": "string",
  "suggestedFixes": [{"type": "code_change|dependency_update|config_change|command_fix", "description": "string", "files": [{"path": "string", "operation": "create|update|delete", "content": "string"}], "commands": ["string"], "priority": number}],
  "preventionTips": ["string"]
}`;
    }
    buildErrorAnalysisUserPrompt(error, context) {
        return `Analyze this error and provide fixes:

ERROR: ${error}

TASK: ${context.task.name} - ${context.task.description}

ERROR LOGS:
${context.errorLogs.join('\n')}

AFFECTED FILES: ${context.affectedFiles.join(', ')}

PREVIOUS ATTEMPTS: ${context.previousAttempts}

Provide specific fixes to resolve this error.`;
    }
    buildTestGenerationSystemPrompt(context) {
        return `You are an expert test engineer. Generate comprehensive tests.

CONTEXT:
- Framework: ${context.framework}
- Test type: ${context.testType}

REQUIREMENTS:
1. Respond with valid JSON only - NO markdown code blocks, NO triple-backtick wrapper
2. Generate complete test files
3. Include setup and teardown
4. Cover edge cases and error scenarios

JSON SCHEMA:
{
  "testFiles": [{"path": "string", "content": "string", "language": "string", "description": "string"}],
  "commands": ["string"],
  "configuration": ["string"]
}`;
    }
    buildTestGenerationUserPrompt(files, context) {
        return `Generate ${context.testType} tests for these files:

FILES: ${files.join(', ')}

FRAMEWORK: ${context.framework}

Generate comprehensive tests that cover functionality, edge cases, and error scenarios.`;
    }
    parsePlanResponse(response, originalPrompt) {
        try {
            // Clean the response by removing markdown code blocks if present
            const cleanedResponse = this.cleanJsonResponse(response);
            const parsed = JSON.parse(cleanedResponse);
            const plan = {
                id: `plan-${Date.now()}`,
                timestamp: Date.now(),
                userPrompt: originalPrompt,
                overview: parsed.overview || 'Generated project plan',
                technologies: parsed.technologies?.map((tech) => ({
                    name: tech.name,
                    version: tech.version,
                    purpose: tech.purpose,
                    category: tech.category
                })) || [],
                architecture: [],
                tasks: parsed.tasks?.map((task, index) => ({
                    id: `task-${Date.now()}-${index}`,
                    name: task.name,
                    description: task.description,
                    agent: task.agent,
                    dependencies: [],
                    status: types_1.TaskStatus.PENDING,
                    priority: task.priority,
                    estimatedDuration: task.estimatedDuration || 30,
                    files: task.files || [],
                    commands: task.commands || [],
                    tests: [],
                    retryCount: 0
                })) || [],
                estimatedDuration: 0,
                status: types_1.PlanStatus.DRAFT
            };
            // Calculate total estimated duration
            plan.estimatedDuration = plan.tasks.reduce((total, task) => total + task.estimatedDuration, 0);
            return plan;
        }
        catch (error) {
            throw new Error(`Failed to parse plan response: ${error}`);
        }
    }
    parseCodeResponse(response) {
        try {
            const cleanedResponse = this.cleanJsonResponse(response);
            const parsed = JSON.parse(cleanedResponse);
            return {
                files: parsed.files || [],
                commands: parsed.commands || [],
                dependencies: parsed.dependencies || [],
                notes: parsed.notes || []
            };
        }
        catch (error) {
            throw new Error(`Failed to parse code response: ${error}`);
        }
    }
    parseErrorAnalysisResponse(response) {
        try {
            const cleanedResponse = this.cleanJsonResponse(response);
            const parsed = JSON.parse(cleanedResponse);
            return {
                rootCause: parsed.rootCause || 'Unknown error',
                suggestedFixes: parsed.suggestedFixes || [],
                preventionTips: parsed.preventionTips || []
            };
        }
        catch (error) {
            throw new Error(`Failed to parse error analysis response: ${error}`);
        }
    }
    parseTestResponse(response) {
        try {
            const cleanedResponse = this.cleanJsonResponse(response);
            const parsed = JSON.parse(cleanedResponse);
            return {
                testFiles: parsed.testFiles || [],
                commands: parsed.commands || [],
                configuration: parsed.configuration || []
            };
        }
        catch (error) {
            throw new Error(`Failed to parse test response: ${error}`);
        }
    }
    cleanJsonResponse(response) {
        // Remove markdown code blocks if present
        let cleaned = response.trim();
        // Remove ```json at the beginning
        if (cleaned.startsWith('```json')) {
            cleaned = cleaned.substring(7);
        }
        else if (cleaned.startsWith('```')) {
            cleaned = cleaned.substring(3);
        }
        // Remove ``` at the end
        if (cleaned.endsWith('```')) {
            cleaned = cleaned.substring(0, cleaned.length - 3);
        }
        return cleaned.trim();
    }
    extractPrimaryLanguages(fileIndex) {
        // Extract language information from file index
        const languages = new Set();
        if (fileIndex.files) {
            fileIndex.files.forEach((file) => {
                if (file.language) {
                    languages.add(file.language);
                }
            });
        }
        return Array.from(languages).slice(0, 3).join(', ') || 'Unknown';
    }
}
exports.GroqClient = GroqClient;
//# sourceMappingURL=GroqClient.js.map