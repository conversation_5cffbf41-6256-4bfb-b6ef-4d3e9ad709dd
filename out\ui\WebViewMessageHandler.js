"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebViewMessageHandler = void 0;
const vscode = __importStar(require("vscode"));
const types_1 = require("../types");
class WebViewMessageHandler {
    constructor(webViewProvider, extension) {
        this.webViewProvider = webViewProvider;
        this.extension = extension;
        // Listen to messages from webview
        this.webViewProvider.onDidReceiveMessage(this.handleMessage.bind(this));
    }
    async handleMessage(message) {
        try {
            switch (message.type) {
                case 'start':
                    await this.handleStart(message.data);
                    break;
                case 'stop':
                    await this.handleStop();
                    break;
                case 'saveConfig':
                    await this.handleSaveConfig(message.data);
                    break;
                case 'loadConfig':
                    await this.handleLoadConfig();
                    break;
                case 'exportLogs':
                    await this.handleExportLogs(message.data);
                    break;
                default:
                    console.warn(`Unknown message type: ${message.type}`);
            }
        }
        catch (error) {
            console.error('Error handling webview message:', error);
            this.webViewProvider.addError(`Message handling error: ${error}`);
        }
    }
    async handleStart(data) {
        if (!data.prompt || !data.prompt.trim()) {
            this.webViewProvider.addError('No prompt provided');
            return;
        }
        try {
            // Start the AutoBuilder process
            await this.extension.startAutoBuilderWithPrompt(data.prompt.trim());
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.webViewProvider.addError(`Failed to start AutoBuilder: ${errorMessage}`);
        }
    }
    async handleStop() {
        try {
            await this.extension.stopAutoBuilder();
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.webViewProvider.addError(`Failed to stop AutoBuilder: ${errorMessage}`);
        }
    }
    async handleSaveConfig(config) {
        try {
            const workspaceConfig = vscode.workspace.getConfiguration('autobuilder');
            await workspaceConfig.update('groqApiKeys', config.apiKeys, vscode.ConfigurationTarget.Workspace);
            await workspaceConfig.update('defaultModel', config.model, vscode.ConfigurationTarget.Workspace);
            await workspaceConfig.update('maxRetries', config.maxRetries, vscode.ConfigurationTarget.Workspace);
            await workspaceConfig.update('enablePlaywright', config.enablePlaywright, vscode.ConfigurationTarget.Workspace);
            this.webViewProvider.addLog(types_1.LogLevel.INFO, 'Config', 'Configuration saved successfully');
            // Reinitialize AI client with new keys
            await this.extension.reinitializeAIClient();
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.webViewProvider.addError(`Failed to save configuration: ${errorMessage}`);
        }
    }
    async handleLoadConfig() {
        try {
            const config = vscode.workspace.getConfiguration('autobuilder');
            const configData = {
                apiKeys: config.get('groqApiKeys') || [],
                model: config.get('defaultModel') || 'llama-3.3-70b-versatile',
                maxRetries: config.get('maxRetries') || 3,
                enablePlaywright: config.get('enablePlaywright') || true
            };
            // Send config to webview
            this.webViewProvider.postMessage({
                type: 'configLoaded',
                data: configData
            });
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.webViewProvider.addError(`Failed to load configuration: ${errorMessage}`);
        }
    }
    async handleExportLogs(logs) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const fileName = `autobuilder-logs-${timestamp}.txt`;
            const uri = await vscode.window.showSaveDialog({
                defaultUri: vscode.Uri.file(fileName),
                filters: {
                    'Text Files': ['txt'],
                    'All Files': ['*']
                }
            });
            if (uri) {
                await vscode.workspace.fs.writeFile(uri, Buffer.from(logs, 'utf8'));
                vscode.window.showInformationMessage(`Logs exported to ${uri.fsPath}`);
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.webViewProvider.addError(`Failed to export logs: ${errorMessage}`);
        }
    }
    // Methods to send data to webview
    updateTasks(tasks) {
        this.webViewProvider.updateTasks(tasks);
    }
    updatePlan(plan) {
        this.webViewProvider.updatePlan(plan);
    }
    updateProgress(progress, currentTask) {
        this.webViewProvider.updateProgress(progress, currentTask);
    }
    addLog(level, agent, message) {
        this.webViewProvider.addLog(level, agent, message);
    }
    addError(error, context) {
        this.webViewProvider.addError(error, context);
    }
    updateStatus(status, message) {
        this.webViewProvider.updateStatus(status, message);
    }
}
exports.WebViewMessageHandler = WebViewMessageHandler;
//# sourceMappingURL=WebViewMessageHandler.js.map