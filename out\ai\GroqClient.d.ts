import { IAIClient, ProjectPlan, PlanningContext, CodeGenerationContext, CodeGenerationResult, ErrorAnalysisContext, ErrorAnalysisResult, TestGenerationContext, TestGenerationResult, Task } from '../types';
export declare class GroqClient implements IAIClient {
    private apiKeys;
    private currentKeyIndex;
    private client;
    private currentModel;
    private rateLimitDelay;
    private lastRequestTime;
    constructor();
    initialize(apiKeys: string[]): Promise<void>;
    private testApiKey;
    generatePlan(prompt: string, context: PlanningContext): Promise<ProjectPlan>;
    generateCode(task: Task, context: CodeGenerationContext): Promise<CodeGenerationResult>;
    analyzeError(error: string, context: ErrorAnalysisContext): Promise<ErrorAnalysisResult>;
    generateTests(files: string[], context: TestGenerationContext): Promise<TestGenerationResult>;
    rotateApiKey(): void;
    getCurrentModel(): string;
    private makeRequest;
    private buildPlanningSystemPrompt;
    private buildPlanningUserPrompt;
    private buildCodeGenerationSystemPrompt;
    private buildCodeGenerationUserPrompt;
    private buildErrorAnalysisSystemPrompt;
    private buildErrorAnalysisUserPrompt;
    private buildTestGenerationSystemPrompt;
    private buildTestGenerationUserPrompt;
    private parsePlanResponse;
    private parseCodeResponse;
    private parseErrorAnalysisResponse;
    private parseTestResponse;
    private cleanJsonResponse;
    private extractPrimaryLanguages;
}
//# sourceMappingURL=GroqClient.d.ts.map