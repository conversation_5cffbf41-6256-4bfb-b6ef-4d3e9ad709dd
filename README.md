# AutoBuilder AI - VS Code Extension

AutoBuilder AI is a powerful VS Code extension that autonomously creates full-stack software applications using AI agents. It behaves like a real software engineer with memory and task reasoning capabilities.

## Features

✅ **Autonomous Development**: Creates complete applications from simple prompts
✅ **Multi-Agent Architecture**: Specialized AI agents for different tasks
✅ **Memory System**: Persistent state and learning across sessions
✅ **Smart Planning**: AI-powered project planning and task breakdown
✅ **Code Generation**: Generates production-ready code in multiple languages
✅ **Terminal Automation**: Automatically runs setup, build, and test commands
✅ **Error Fixing**: Analyzes and fixes issues automatically
✅ **Testing Integration**: Supports Playwright and other testing frameworks

## AI Agents

- **Architect**: Creates comprehensive project plans and architecture
- **Engineer**: Generates and writes code files
- **Tester**: Creates and runs tests
- **Fixer**: Analyzes errors and provides fixes
- **MemoryKeeper**: Manages persistent state and memory
- **Indexer**: Scans and indexes workspace files

## Quick Start

1. **Install the Extension**: Install AutoBuilder AI from the VS Code marketplace
2. **Open the Sidebar**: Click the 🤖 robot icon in the VS Code Activity Bar
3. **Configure API Keys**: Add your GroqCloud API keys in the Configuration section
4. **Start Building**: Enter your project description and click "Start Building"

## Configuration

Add your GroqCloud API keys to VS Code settings:

```json
{
  "autobuilder.groqApiKeys": [
    "your-groq-api-key-1",
    "your-groq-api-key-2"
  ],
  "autobuilder.defaultModel": "llama-3.3-70b-versatile",
  "autobuilder.maxRetries": 3,
  "autobuilder.enablePlaywright": true
}
```

## Example Usage

1. Open an empty folder in VS Code
2. Click the 🤖 AutoBuilder AI icon in the sidebar
3. Enter a prompt like: "Build a Next.js app with MongoDB and Tailwind CSS for a todo list"
4. Click "Start Building" and watch as AutoBuilder AI:
   - Analyzes your requirements
   - Creates a detailed project plan
   - Generates all necessary files
   - Sets up the development environment
   - Runs tests and fixes any issues

## Memory System

AutoBuilder AI maintains a `.memory` folder in your workspace with:

- `index.json`: File structure and content summaries
- `plan.json`: Active build plan and architecture
- `tasks.json`: Task progress and status
- `prompts.log`: Conversation history with AI agents

## Commands

- `AutoBuilder: Start` - Begin autonomous development
- `AutoBuilder: Stop` - Stop current operation
- `AutoBuilder: Regenerate` - Regenerate current task
- `AutoBuilder: Fix` - Analyze and fix current issues
- `AutoBuilder: Deploy` - Deploy the application

## Supported Technologies

AutoBuilder AI can work with any technology stack, including:

**Frontend**: React, Vue, Angular, Svelte, Next.js, Nuxt.js
**Backend**: Node.js, Express, Fastify, NestJS, Python, Django, Flask
**Databases**: MongoDB, PostgreSQL, MySQL, SQLite, Supabase
**Styling**: Tailwind CSS, CSS Modules, Styled Components, SCSS
**Testing**: Jest, Vitest, Cypress, Playwright
**Deployment**: Vercel, Netlify, AWS, Azure, Google Cloud

## Requirements

- VS Code 1.74.0 or higher
- GroqCloud API key (get one at https://groq.com)
- Node.js (for JavaScript/TypeScript projects)

## Development

To contribute to AutoBuilder AI:

1. Clone the repository
2. Run `npm install`
3. Run `npm run compile`
4. Press F5 to launch a new VS Code window with the extension

## License

MIT License - see LICENSE file for details

## Support

For issues and feature requests, please visit our GitHub repository.

---

**AutoBuilder AI** - Building the future of autonomous software development
