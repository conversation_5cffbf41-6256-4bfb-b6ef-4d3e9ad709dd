{"version": 3, "file": "WebViewMessageHandler.js", "sourceRoot": "", "sources": ["../../src/ui/WebViewMessageHandler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,oCAAqD;AAOrD,MAAa,qBAAqB;IAIhC,YAAY,eAA2C,EAAE,SAA+B;QACtF,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,kCAAkC;QAClC,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1E,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAuB;QACjD,IAAI;YACF,QAAQ,OAAO,CAAC,IAAI,EAAE;gBACpB,KAAK,OAAO;oBACV,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACrC,MAAM;gBAER,KAAK,MAAM;oBACT,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;oBACxB,MAAM;gBAER,KAAK,YAAY;oBACf,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC1C,MAAM;gBAER,KAAK,YAAY;oBACf,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBAC9B,MAAM;gBAER,KAAK,YAAY;oBACf,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC1C,MAAM;gBAER;oBACE,OAAO,CAAC,IAAI,CAAC,yBAAyB,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;aACzD;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;SACnE;IACH,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,IAAwB;QAChD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE;YACvC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;YACpD,OAAO;SACR;QAED,IAAI;YACF,gCAAgC;YAChC,MAAM,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;SACrE;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,gCAAgC,YAAY,EAAE,CAAC,CAAC;SAC/E;IACH,CAAC;IAEO,KAAK,CAAC,UAAU;QACtB,IAAI;YACF,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;SACxC;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,+BAA+B,YAAY,EAAE,CAAC,CAAC;SAC9E;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,MAK9B;QACC,IAAI;YACF,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAEzE,MAAM,eAAe,CAAC,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;YAClG,MAAM,eAAe,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;YACjG,MAAM,eAAe,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;YACpG,MAAM,eAAe,CAAC,MAAM,CAAC,kBAAkB,EAAE,MAAM,CAAC,gBAAgB,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;YAEhH,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,gBAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,kCAAkC,CAAC,CAAC;YAEzF,uCAAuC;YACvC,MAAM,IAAI,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC;SAC7C;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,iCAAiC,YAAY,EAAE,CAAC,CAAC;SAChF;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAEhE,MAAM,UAAU,GAAG;gBACjB,OAAO,EAAE,MAAM,CAAC,GAAG,CAAW,aAAa,CAAC,IAAI,EAAE;gBAClD,KAAK,EAAE,MAAM,CAAC,GAAG,CAAS,cAAc,CAAC,IAAI,yBAAyB;gBACtE,UAAU,EAAE,MAAM,CAAC,GAAG,CAAS,YAAY,CAAC,IAAI,CAAC;gBACjD,gBAAgB,EAAE,MAAM,CAAC,GAAG,CAAU,kBAAkB,CAAC,IAAI,IAAI;aAClE,CAAC;YAEF,yBAAyB;YACzB,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;gBAC/B,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,UAAU;aACjB,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,iCAAiC,YAAY,EAAE,CAAC,CAAC;SAChF;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,IAAY;QACzC,IAAI;YACF,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACjE,MAAM,QAAQ,GAAG,oBAAoB,SAAS,MAAM,CAAC;YAErD,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC7C,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACrC,OAAO,EAAE;oBACP,YAAY,EAAE,CAAC,KAAK,CAAC;oBACrB,WAAW,EAAE,CAAC,GAAG,CAAC;iBACnB;aACF,CAAC,CAAC;YAEH,IAAI,GAAG,EAAE;gBACP,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;gBACpE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oBAAoB,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;aACxE;SACF;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,0BAA0B,YAAY,EAAE,CAAC,CAAC;SACzE;IACH,CAAC;IAED,kCAAkC;IAC3B,WAAW,CAAC,KAAY;QAC7B,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAEM,UAAU,CAAC,IAAS;QACzB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAEM,cAAc,CAAC,QAAgB,EAAE,WAAoB;QAC1D,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IAC7D,CAAC;IAEM,MAAM,CAAC,KAAe,EAAE,KAAa,EAAE,OAAe;QAC3D,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;IAEM,QAAQ,CAAC,KAAa,EAAE,OAAa;QAC1C,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAEM,YAAY,CAAC,MAAuB,EAAE,OAAgB;QAC3D,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;CACF;AAjKD,sDAiKC"}