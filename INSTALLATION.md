# AutoBuilder AI - Installation Guide

## Prerequisites

1. **VS Code**: Version 1.74.0 or higher
2. **Node.js**: Version 16 or higher
3. **GroqCloud API Key**: Get one from [https://groq.com](https://groq.com)

## Installation Steps

### 1. Install the Extension

#### Option A: From Source (Development)
```bash
# Clone or download the extension files
cd path/to/autobuilder-ai-extension

# Install dependencies
npm install

# Compile TypeScript
npm run compile

# Package the extension (optional)
npm run package
```

#### Option B: Install VSIX (if packaged)
1. Open VS Code
2. Go to Extensions view (Ctrl+Shift+X)
3. Click "..." menu → "Install from VSIX..."
4. Select the `.vsix` file

### 2. Configure API Keys

1. Open VS Code Settings (Ctrl+,)
2. Search for "autobuilder"
3. Add your GroqCloud API keys:

```json
{
  "autobuilder.groqApiKeys": [
    "your-groq-api-key-1",
    "your-groq-api-key-2"
  ],
  "autobuilder.defaultModel": "llama-3.3-70b-versatile",
  "autobuilder.maxRetries": 3,
  "autobuilder.enablePlaywright": true
}
```

### 3. Test the Extension

1. Open a new folder in VS Code
2. Press `Ctrl+Shift+P` to open command palette
3. Type "AutoBuilder: Start"
4. Enter a prompt like: "Create a simple React app with TypeScript"

## Configuration Options

| Setting | Description | Default |
|---------|-------------|---------|
| `autobuilder.groqApiKeys` | Array of GroqCloud API keys for rotation | `[]` |
| `autobuilder.defaultModel` | AI model to use | `"llama-3.3-70b-versatile"` |
| `autobuilder.maxRetries` | Maximum retries for failed operations | `3` |
| `autobuilder.enablePlaywright` | Enable Playwright testing | `true` |
| `autobuilder.memoryPath` | Memory folder path | `".memory"` |

## Available Commands

- `AutoBuilder: Start` - Begin autonomous development
- `AutoBuilder: Stop` - Stop current operation  
- `AutoBuilder: Regenerate` - Regenerate current task
- `AutoBuilder: Fix` - Analyze and fix issues
- `AutoBuilder: Deploy` - Deploy application

## Troubleshooting

### Extension Not Loading
- Check VS Code version (must be 1.74.0+)
- Ensure all dependencies are installed: `npm install`
- Recompile TypeScript: `npm run compile`

### API Key Issues
- Verify your GroqCloud API key is valid
- Check rate limits on your account
- Try adding multiple API keys for rotation

### Memory Issues
- Clear the `.memory` folder in your workspace
- Restart VS Code
- Check workspace permissions

### Build Failures
- Ensure Node.js is installed and accessible
- Check internet connection for package downloads
- Verify workspace has write permissions

## Development Setup

For contributing to AutoBuilder AI:

```bash
# Clone the repository
git clone <repository-url>
cd autobuilder-ai

# Install dependencies
npm install

# Start development
npm run watch

# Run tests
npm test

# Package for distribution
npm run package
```

## File Structure

```
autobuilder-ai/
├── src/
│   ├── agents/           # AI agent implementations
│   ├── ai/              # AI client and integration
│   ├── memory/          # Memory system
│   ├── types/           # TypeScript type definitions
│   ├── utils/           # Utility classes
│   └── extension.ts     # Main extension entry point
├── out/                 # Compiled JavaScript
├── package.json         # Extension manifest
├── tsconfig.json        # TypeScript configuration
└── README.md           # Documentation
```

## Support

- **Issues**: Report bugs and feature requests on GitHub
- **Documentation**: Check README.md for detailed usage
- **Community**: Join our Discord server for help and discussions

## Next Steps

After installation:
1. Configure your API keys
2. Open a project folder
3. Try the "AutoBuilder: Start" command
4. Explore the generated `.memory` folder
5. Check the Output panel for detailed logs

Happy building with AutoBuilder AI! 🚀
