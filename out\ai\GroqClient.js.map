{"version": 3, "file": "GroqClient.js", "sourceRoot": "", "sources": ["../../src/ai/GroqClient.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA4D;AAC5D,oCAiBkB;AAelB,MAAa,UAAU;IAQrB;QAPQ,YAAO,GAAa,EAAE,CAAC;QACvB,oBAAe,GAAW,CAAC,CAAC;QAE5B,iBAAY,GAAW,yBAAyB,CAAC;QACjD,mBAAc,GAAW,IAAI,CAAC,CAAC,4BAA4B;QAC3D,oBAAe,GAAW,CAAC,CAAC;QAGlC,IAAI,CAAC,MAAM,GAAG,eAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,gCAAgC;YACzC,OAAO,EAAE,KAAK;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;aACnC;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,OAAiB;QAChC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;SACrD;QAED,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACnE,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzB,yBAAyB;QACzB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;IAC1B,CAAC;IAEO,KAAK,CAAC,UAAU;QACtB,IAAI;YACF,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC;SACxF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;SAC7D;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAc,EAAE,OAAwB;QACzD,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QAC7D,MAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAEjE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,UAAU,EAAE;YAChE,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,GAAG;SACjB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,IAAU,EAAE,OAA8B;QAC3D,MAAM,YAAY,GAAG,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,CAAC;QACnE,MAAM,UAAU,GAAG,IAAI,CAAC,6BAA6B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAErE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,UAAU,EAAE;YAChE,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,GAAG;SACjB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAa,EAAE,OAA6B;QAC7D,MAAM,YAAY,GAAG,IAAI,CAAC,8BAA8B,EAAE,CAAC;QAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAErE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,UAAU,EAAE;YAChE,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,GAAG;SACjB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;IACnD,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,KAAe,EAAE,OAA8B;QACjE,MAAM,YAAY,GAAG,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,CAAC;QACnE,MAAM,UAAU,GAAG,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAEtE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,UAAU,EAAE;YAChE,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,GAAG;SACjB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAED,YAAY;QACV,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;SACzE;IACH,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAEO,KAAK,CAAC,WAAW,CACvB,YAAoB,EACpB,UAAkB,EAClB,OAAmD;QAEnD,gBAAgB;QAChB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,oBAAoB,GAAG,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC;QACxD,IAAI,oBAAoB,GAAG,IAAI,CAAC,cAAc,EAAE;YAC9C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,GAAG,oBAAoB,CAAC,CAAC,CAAC;SAC/F;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAEtD,IAAI;YACF,MAAM,QAAQ,GAAgC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBACxF,KAAK,EAAE,IAAI,CAAC,YAAY;gBACxB,QAAQ,EAAE;oBACR,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE;oBACzC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE;iBACtC;gBACD,UAAU,EAAE,OAAO,CAAC,SAAS;gBAC7B,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,MAAM,EAAE,KAAK;aACd,EAAE;gBACD,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,UAAU,EAAE;iBACxC;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAElC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBAChE,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;aAC9C;YAED,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;SACjD;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;gBAC7B,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE;oBAClC,gCAAgC;oBAChC,IAAI,CAAC,YAAY,EAAE,CAAC;oBACpB,IAAI,IAAI,CAAC,eAAe,KAAK,CAAC,EAAE;wBAC9B,uBAAuB;wBACvB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;qBAC7C;oBACD,sBAAsB;oBACtB,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;iBAC5D;qBAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE;oBACzC,+BAA+B;oBAC/B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;oBACxD,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;iBAC5D;aACF;YACD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAEO,yBAAyB,CAAC,OAAwB;QACxD,OAAO;;;kBAGO,OAAO,CAAC,cAAc,CAAC,UAAU;uBAC5B,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,cAAc,CAAC;mBACxD,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;;;;;;;;;;;;;;EAcpD,CAAC;IACD,CAAC;IAEO,uBAAuB,CAAC,MAAc,EAAE,OAAwB;QACtE,MAAM,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,IAAI,iBAAiB,CAAC;QAC1E,MAAM,gBAAgB,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;YAC7C,kBAAkB,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC;YACnD,kBAAkB,CAAC;QAErB,OAAO,uCAAuC,MAAM;;;EAGtD,aAAa;;EAEb,gBAAgB;;;EAGhB,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB;;2FAEJ,CAAC;IAC1F,CAAC;IAEO,+BAA+B,CAAC,OAA8B;QACpE,OAAO;;;sBAGW,OAAO,CAAC,eAAe,IAAI,eAAe;oBAC5C,OAAO,CAAC,aAAa,CAAC,MAAM;kBAC9B,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;EAe/C,CAAC;IACD,CAAC;IAEO,6BAA6B,CAAC,IAAU,EAAE,OAA8B;QAC9E,OAAO,2BAA2B,IAAI,CAAC,IAAI;;eAEhC,IAAI,CAAC,WAAW;;iBAEd,OAAO,CAAC,IAAI,CAAC,QAAQ;;gBAEtB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;kBAEvE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;;gBAElC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;;iFAE4C,CAAC;IAChF,CAAC;IAEO,8BAA8B;QACpC,OAAO;;;;;;;;;;;;;EAaT,CAAC;IACD,CAAC;IAEO,4BAA4B,CAAC,KAAa,EAAE,OAA6B;QAC/E,OAAO;;SAEF,KAAK;;QAEN,OAAO,CAAC,IAAI,CAAC,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW;;;EAGrD,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;;kBAEZ,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;;qBAE7B,OAAO,CAAC,gBAAgB;;8CAEC,CAAC;IAC7C,CAAC;IAEO,+BAA+B,CAAC,OAA8B;QACpE,OAAO;;;eAGI,OAAO,CAAC,SAAS;eACjB,OAAO,CAAC,QAAQ;;;;;;;;;;;;;EAa7B,CAAC;IACD,CAAC;IAEO,6BAA6B,CAAC,KAAe,EAAE,OAA8B;QACnF,OAAO,YAAY,OAAO,CAAC,QAAQ;;SAE9B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;;aAEZ,OAAO,CAAC,SAAS;;wFAE0D,CAAC;IACvF,CAAC;IAEO,iBAAiB,CAAC,QAAgB,EAAE,cAAsB;QAChE,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEpC,MAAM,IAAI,GAAgB;gBACxB,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;gBACxB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,UAAU,EAAE,cAAc;gBAC1B,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,wBAAwB;gBACrD,YAAY,EAAE,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;oBACrD,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,QAAQ,EAAE,IAAI,CAAC,QAAwB;iBACxC,CAAC,CAAC,IAAI,EAAE;gBACT,YAAY,EAAE,EAAE;gBAChB,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,IAAS,EAAE,KAAa,EAAE,EAAE,CAAC,CAAC;oBACtD,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,KAAK,EAAE;oBACjC,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,KAAK,EAAE,IAAI,CAAC,KAAkB;oBAC9B,YAAY,EAAE,EAAE;oBAChB,MAAM,EAAE,kBAAU,CAAC,OAAO;oBAC1B,QAAQ,EAAE,IAAI,CAAC,QAAwB;oBACvC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,IAAI,EAAE;oBAC/C,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;oBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,EAAE;oBAC7B,KAAK,EAAE,EAAE;oBACT,UAAU,EAAE,CAAC;iBACd,CAAC,CAAC,IAAI,EAAE;gBACT,iBAAiB,EAAE,CAAC;gBACpB,MAAM,EAAE,kBAAU,CAAC,KAAK;aACzB,CAAC;YAEF,qCAAqC;YACrC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;YAE/F,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;SAC5D;IACH,CAAC;IAEO,iBAAiB,CAAC,QAAgB;QACxC,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEpC,OAAO;gBACL,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;gBACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,EAAE;gBAC/B,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,EAAE;gBACvC,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;aAC1B,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;SAC5D;IACH,CAAC;IAEO,0BAA0B,CAAC,QAAgB;QACjD,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEpC,OAAO;gBACL,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,eAAe;gBAC9C,cAAc,EAAE,MAAM,CAAC,cAAc,IAAI,EAAE;gBAC3C,cAAc,EAAE,MAAM,CAAC,cAAc,IAAI,EAAE;aAC5C,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,4CAA4C,KAAK,EAAE,CAAC,CAAC;SACtE;IACH,CAAC;IAEO,iBAAiB,CAAC,QAAgB;QACxC,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEpC,OAAO;gBACL,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,EAAE;gBACjC,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,EAAE;gBAC/B,aAAa,EAAE,MAAM,CAAC,aAAa,IAAI,EAAE;aAC1C,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;SAC5D;IACH,CAAC;IAEO,uBAAuB,CAAC,SAAc;QAC5C,+CAA+C;QAC/C,MAAM,SAAS,GAAG,IAAI,GAAG,EAAU,CAAC;QAEpC,IAAI,SAAS,CAAC,KAAK,EAAE;YACnB,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;gBACpC,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACjB,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBAC9B;YACH,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC;IACnE,CAAC;CACF;AAjZD,gCAiZC"}