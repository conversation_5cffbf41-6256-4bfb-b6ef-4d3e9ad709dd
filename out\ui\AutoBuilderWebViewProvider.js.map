{"version": 3, "file": "AutoBuilderWebViewProvider.js", "sourceRoot": "", "sources": ["../../src/ui/AutoBuilderWebViewProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,oCAQkB;AAElB,MAAa,0BAA0B;IAUrC,YAAY,YAAwB,EAAE,OAAgC;QAH9D,yBAAoB,GAA6B,IAAI,MAAM,CAAC,YAAY,EAAO,CAAC;QACxE,wBAAmB,GAAsB,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAGvF,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG;YACd,MAAM,EAAE,uBAAe,CAAC,IAAI;YAC5B,QAAQ,EAAE,CAAC;YACX,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,EAAE;SACX,CAAC;IACJ,CAAC;IAEM,kBAAkB,CACvB,WAA+B,EAC/B,OAAyC,EACzC,MAAgC;QAEhC,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;QAEzB,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG;YAC5B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE;gBAClB,IAAI,CAAC,aAAa;aACnB;SACF,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAExE,mCAAmC;QACnC,WAAW,CAAC,OAAO,CAAC,mBAAmB,CACrC,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,CAAC,EACD,SAAS,EACT,IAAI,CAAC,QAAQ,CAAC,aAAa,CAC5B,CAAC;QAEF,qBAAqB;QACrB,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAEM,YAAY,CAAC,MAAuB,EAAE,OAAgB;QAC3D,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;QAC9B,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,MAAM,CAAC,gBAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;SAC/C;QACD,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAEM,cAAc,CAAC,QAAgB,EAAE,WAAoB;QAC1D,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAClC,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;QACxC,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAEM,MAAM,CAAC,KAAe,EAAE,KAAa,EAAE,OAAe;QAC3D,MAAM,QAAQ,GAAa;YACzB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,KAAK;YACL,KAAK;YACL,OAAO;SACR,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAElC,0BAA0B;QAC1B,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE;YACnC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;SAC5B;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAEM,QAAQ,CAAC,KAAa,EAAE,OAAa;QAC1C,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;YACxB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,SAAS;YAC5C,KAAK;YACL,OAAO;SACR,CAAC,CAAC;QAEH,2BAA2B;QAC3B,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,EAAE;YACpC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;SAC9B;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAEM,WAAW,CAAC,KAAa;QAC9B,IAAI,CAAC,WAAW,CAAC;YACf,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;IACL,CAAC;IAEM,UAAU,CAAC,IAAwB;QACxC,IAAI,CAAC,WAAW,CAAC;YACf,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,IAAI;SACX,CAAC,CAAC;IACL,CAAC;IAEO,QAAQ;QACd,IAAI,CAAC,WAAW,CAAC;YACf,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,IAAI,CAAC,QAAQ;SACpB,CAAC,CAAC;IACL,CAAC;IAEM,WAAW,CAAC,OAAY;QAC7B,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SACzC;IACH,CAAC;IAEO,kBAAkB,CAAC,OAAuB;QAChD,4GAA4G;QAC5G,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;QACpG,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;QAC1G,MAAM,cAAc,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;QAC5G,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;QAExG,yDAAyD;QACzD,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAC;QAEzB,OAAO;;;;4FAIiF,OAAO,CAAC,SAAS,uBAAuB,KAAK;;sBAEnH,aAAa;sBACb,cAAc;sBACd,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yBA+HT,KAAK,UAAU,SAAS;;cAEnC,CAAC;IACb,CAAC;;AAjRH,gEAkRC;AAjRwB,mCAAQ,GAAG,qBAAqB,CAAC;AAmR1D,SAAS,QAAQ;IACf,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,MAAM,QAAQ,GAAG,gEAAgE,CAAC;IAClF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;QAC3B,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;KACtE;IACD,OAAO,IAAI,CAAC;AACd,CAAC"}