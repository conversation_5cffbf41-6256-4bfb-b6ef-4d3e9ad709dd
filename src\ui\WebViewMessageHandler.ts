import * as vscode from 'vscode';
import { AutoBuilderWebViewProvider } from './AutoBuilderWebViewProvider';
import { AutoBuilderExtension } from '../AutoBuilderExtension';
import { LogLevel, ExtensionStatus } from '../types';

export interface WebViewMessage {
  type: string;
  data?: any;
}

export class WebViewMessageHandler {
  private webViewProvider: AutoBuilderWebViewProvider;
  private extension: AutoBuilderExtension;

  constructor(webViewProvider: AutoBuilderWebViewProvider, extension: AutoBuilderExtension) {
    this.webViewProvider = webViewProvider;
    this.extension = extension;
    
    // Listen to messages from webview
    this.webViewProvider.onDidReceiveMessage(this.handleMessage.bind(this));
  }

  private async handleMessage(message: WebViewMessage): Promise<void> {
    try {
      switch (message.type) {
        case 'start':
          await this.handleStart(message.data);
          break;
        
        case 'stop':
          await this.handleStop();
          break;
        
        case 'saveConfig':
          await this.handleSaveConfig(message.data);
          break;
        
        case 'loadConfig':
          await this.handleLoadConfig();
          break;
        
        case 'exportLogs':
          await this.handleExportLogs(message.data);
          break;
        
        default:
          console.warn(`Unknown message type: ${message.type}`);
      }
    } catch (error) {
      console.error('Error handling webview message:', error);
      this.webViewProvider.addError(`Message handling error: ${error}`);
    }
  }

  private async handleStart(data: { prompt: string }): Promise<void> {
    if (!data.prompt || !data.prompt.trim()) {
      this.webViewProvider.addError('No prompt provided');
      return;
    }

    try {
      // Start the AutoBuilder process
      await this.extension.startAutoBuilderWithPrompt(data.prompt.trim());
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.webViewProvider.addError(`Failed to start AutoBuilder: ${errorMessage}`);
    }
  }

  private async handleStop(): Promise<void> {
    try {
      await this.extension.stopAutoBuilder();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.webViewProvider.addError(`Failed to stop AutoBuilder: ${errorMessage}`);
    }
  }

  private async handleSaveConfig(config: {
    apiKeys: string[];
    model: string;
    maxRetries: number;
    enablePlaywright: boolean;
  }): Promise<void> {
    try {
      const workspaceConfig = vscode.workspace.getConfiguration('autobuilder');
      
      await workspaceConfig.update('groqApiKeys', config.apiKeys, vscode.ConfigurationTarget.Workspace);
      await workspaceConfig.update('defaultModel', config.model, vscode.ConfigurationTarget.Workspace);
      await workspaceConfig.update('maxRetries', config.maxRetries, vscode.ConfigurationTarget.Workspace);
      await workspaceConfig.update('enablePlaywright', config.enablePlaywright, vscode.ConfigurationTarget.Workspace);
      
      this.webViewProvider.addLog(LogLevel.INFO, 'Config', 'Configuration saved successfully');
      
      // Reinitialize AI client with new keys
      await this.extension.reinitializeAIClient();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.webViewProvider.addError(`Failed to save configuration: ${errorMessage}`);
    }
  }

  private async handleLoadConfig(): Promise<void> {
    try {
      const config = vscode.workspace.getConfiguration('autobuilder');
      
      const configData = {
        apiKeys: config.get<string[]>('groqApiKeys') || [],
        model: config.get<string>('defaultModel') || 'llama-3.3-70b-versatile',
        maxRetries: config.get<number>('maxRetries') || 3,
        enablePlaywright: config.get<boolean>('enablePlaywright') || true
      };
      
      // Send config to webview
      this.webViewProvider.postMessage({
        type: 'configLoaded',
        data: configData
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.webViewProvider.addError(`Failed to load configuration: ${errorMessage}`);
    }
  }

  private async handleExportLogs(logs: string): Promise<void> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `autobuilder-logs-${timestamp}.txt`;
      
      const uri = await vscode.window.showSaveDialog({
        defaultUri: vscode.Uri.file(fileName),
        filters: {
          'Text Files': ['txt'],
          'All Files': ['*']
        }
      });
      
      if (uri) {
        await vscode.workspace.fs.writeFile(uri, Buffer.from(logs, 'utf8'));
        vscode.window.showInformationMessage(`Logs exported to ${uri.fsPath}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.webViewProvider.addError(`Failed to export logs: ${errorMessage}`);
    }
  }

  // Methods to send data to webview
  public updateTasks(tasks: any[]): void {
    this.webViewProvider.updateTasks(tasks);
  }

  public updatePlan(plan: any): void {
    this.webViewProvider.updatePlan(plan);
  }

  public updateProgress(progress: number, currentTask?: string): void {
    this.webViewProvider.updateProgress(progress, currentTask);
  }

  public addLog(level: LogLevel, agent: string, message: string): void {
    this.webViewProvider.addLog(level, agent, message);
  }

  public addError(error: string, context?: any): void {
    this.webViewProvider.addError(error, context);
  }

  public updateStatus(status: ExtensionStatus, message?: string): void {
    this.webViewProvider.updateStatus(status, message);
  }
}
