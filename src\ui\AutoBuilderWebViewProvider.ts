import * as vscode from 'vscode';
import * as path from 'path';
import { 
  ExtensionState, 
  UIState, 
  ExtensionStatus, 
  LogEntry, 
  Task, 
  ProjectPlan,
  LogLevel 
} from '../types';

export class AutoBuilderWebViewProvider implements vscode.WebviewViewProvider {
  public static readonly viewType = 'autobuilder.sidebar';
  
  private _view?: vscode.WebviewView;
  private _extensionUri: vscode.Uri;
  private _context: vscode.ExtensionContext;
  private _uiState: UIState;
  private _onDidReceiveMessage: vscode.EventEmitter<any> = new vscode.EventEmitter<any>();
  public readonly onDidReceiveMessage: vscode.Event<any> = this._onDidReceiveMessage.event;

  constructor(extensionUri: vscode.Uri, context: vscode.ExtensionContext) {
    this._extensionUri = extensionUri;
    this._context = context;
    this._uiState = {
      status: ExtensionStatus.IDLE,
      progress: 0,
      logs: [],
      errors: []
    };
  }

  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken,
  ) {
    this._view = webviewView;

    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [
        this._extensionUri
      ]
    };

    webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

    // Handle messages from the webview
    webviewView.webview.onDidReceiveMessage(
      message => {
        this._onDidReceiveMessage.fire(message);
      },
      undefined,
      this._context.subscriptions
    );

    // Send initial state
    this.updateUI();
  }

  public updateStatus(status: ExtensionStatus, message?: string): void {
    this._uiState.status = status;
    if (message) {
      this.addLog(LogLevel.INFO, 'System', message);
    }
    this.updateUI();
  }

  public updateProgress(progress: number, currentTask?: string): void {
    this._uiState.progress = progress;
    this._uiState.currentTask = currentTask;
    this.updateUI();
  }

  public addLog(level: LogLevel, agent: string, message: string): void {
    const logEntry: LogEntry = {
      timestamp: Date.now(),
      level,
      agent,
      message
    };

    this._uiState.logs.push(logEntry);
    
    // Keep only last 100 logs
    if (this._uiState.logs.length > 100) {
      this._uiState.logs.shift();
    }

    this.updateUI();
  }

  public addError(error: string, context?: any): void {
    this._uiState.errors.push({
      timestamp: Date.now(),
      task: this._uiState.currentTask || 'Unknown',
      error,
      context
    });

    // Keep only last 20 errors
    if (this._uiState.errors.length > 20) {
      this._uiState.errors.shift();
    }

    this.updateUI();
  }

  public updateTasks(tasks: Task[]): void {
    this.postMessage({
      type: 'updateTasks',
      data: tasks
    });
  }

  public updatePlan(plan: ProjectPlan | null): void {
    this.postMessage({
      type: 'updatePlan',
      data: plan
    });
  }

  private updateUI(): void {
    this.postMessage({
      type: 'updateState',
      data: this._uiState
    });
  }

  public postMessage(message: any): void {
    if (this._view) {
      this._view.webview.postMessage(message);
    }
  }

  private _getHtmlForWebview(webview: vscode.Webview): string {
    // Get the local path to main script run in the webview, then convert it to a uri we can use in the webview.
    const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'main.js'));
    const styleResetUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'reset.css'));
    const styleVSCodeUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'vscode.css'));
    const styleMainUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'main.css'));

    // Use a nonce to only allow a specific script to be run.
    const nonce = getNonce();

    return `<!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}';">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="${styleResetUri}" rel="stylesheet">
        <link href="${styleVSCodeUri}" rel="stylesheet">
        <link href="${styleMainUri}" rel="stylesheet">
        <title>AutoBuilder AI</title>
      </head>
      <body>
        <div class="container">
          <!-- Header -->
          <div class="header">
            <h1 class="title">
              <span class="icon">🤖</span>
              AutoBuilder AI
            </h1>
            <div class="status-indicator" id="statusIndicator">
              <span class="status-text" id="statusText">Ready</span>
            </div>
          </div>

          <!-- Input Section -->
          <div class="section">
            <h2>Project Prompt</h2>
            <div class="input-group">
              <textarea 
                id="promptInput" 
                placeholder="Describe the application you want to build...&#10;&#10;Examples:&#10;• Build a Next.js app with MongoDB and Tailwind CSS&#10;• Create a React todo app with TypeScript&#10;• Build a Node.js REST API with Express and PostgreSQL"
                rows="4"
              ></textarea>
              <div class="button-group">
                <button id="startBtn" class="primary-button">
                  <span class="button-icon">▶️</span>
                  Start Building
                </button>
                <button id="stopBtn" class="secondary-button" disabled>
                  <span class="button-icon">⏹️</span>
                  Stop
                </button>
              </div>
            </div>
          </div>

          <!-- Progress Section -->
          <div class="section" id="progressSection" style="display: none;">
            <h2>Progress</h2>
            <div class="progress-container">
              <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
              </div>
              <div class="progress-text" id="progressText">0%</div>
            </div>
            <div class="current-task" id="currentTask"></div>
          </div>

          <!-- Tasks Section -->
          <div class="section">
            <h2>Tasks</h2>
            <div class="task-list" id="taskList">
              <div class="empty-state">No tasks yet. Start building to see tasks here.</div>
            </div>
          </div>

          <!-- Logs Section -->
          <div class="section">
            <h2>Activity Log</h2>
            <div class="log-container">
              <div class="log-controls">
                <button id="clearLogsBtn" class="small-button">Clear</button>
                <button id="exportLogsBtn" class="small-button">Export</button>
              </div>
              <div class="log-list" id="logList">
                <div class="log-entry info">
                  <span class="log-time">Ready</span>
                  <span class="log-agent">System</span>
                  <span class="log-message">AutoBuilder AI is ready to build your application</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Configuration Section -->
          <div class="section collapsible">
            <h2 class="collapsible-header" id="configHeader">
              Configuration
              <span class="collapse-icon">▼</span>
            </h2>
            <div class="collapsible-content" id="configContent">
              <div class="config-group">
                <label for="apiKeysInput">GroqCloud API Keys</label>
                <textarea 
                  id="apiKeysInput" 
                  placeholder="Enter your GroqCloud API keys (one per line)"
                  rows="3"
                ></textarea>
                <small>Get your API keys from <a href="https://groq.com" target="_blank">groq.com</a></small>
              </div>
              
              <div class="config-group">
                <label for="modelSelect">AI Model</label>
                <select id="modelSelect">
                  <option value="llama-3.3-70b-versatile">Llama 3.3 70B Versatile (Recommended)</option>
                  <option value="llama-3.1-8b-instant">Llama 3.1 8B Instant</option>
                  <option value="qwen/qwen3-32b">Qwen3 32B</option>
                  <option value="meta-llama/llama-4-scout-17b-16e-instruct">Llama 4 Scout 17B</option>
                  <option value="meta-llama/llama-4-maverick-17b-128e-instruct">Llama 4 Maverick 17B</option>
                </select>
              </div>

              <div class="config-group">
                <label for="maxRetriesInput">Max Retries</label>
                <input type="number" id="maxRetriesInput" value="3" min="1" max="10">
              </div>

              <div class="config-group">
                <label>
                  <input type="checkbox" id="playwrightCheckbox" checked>
                  Enable Playwright Testing
                </label>
              </div>

              <button id="saveConfigBtn" class="primary-button">Save Configuration</button>
            </div>
          </div>

          <!-- Errors Section -->
          <div class="section" id="errorsSection" style="display: none;">
            <h2>Errors</h2>
            <div class="error-list" id="errorList"></div>
          </div>
        </div>

        <script nonce="${nonce}" src="${scriptUri}"></script>
      </body>
      </html>`;
  }
}

function getNonce() {
  let text = '';
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  for (let i = 0; i < 32; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length));
  }
  return text;
}
